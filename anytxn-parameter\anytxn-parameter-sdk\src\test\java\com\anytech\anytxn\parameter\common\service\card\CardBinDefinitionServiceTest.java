package com.anytech.anytxn.parameter.common.service.card;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinControlReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinControlResDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBinControl;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBinDefinition;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinControlMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinControlSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinDefinitionMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinDefinitionSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.unicast.BinCardNumberUsedSelfMapper;
import com.anytech.anytxn.parameter.card.service.CardBinDefinitionServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardBinDefinitionService测试类
 * 测试卡片BIN定义服务的实现类：CardBinDefinitionServiceImpl
 * 
 * <AUTHOR>
 * @date 2024/03/21
 */
@ExtendWith(MockitoExtension.class)
class CardBinDefinitionServiceTest {

    @Mock
    private ParmCardBinControlMapper cardBinControlMapper;

    @Mock
    private ParmCardBinControlSelfMapper cardBinControlSelfMapper;

    @Mock
    private BinCardNumberUsedSelfMapper binCardNumberUsedSelfMapper;

    @Mock
    private ParmCardBinDefinitionMapper cardBinDefinitionMapper;

    @Mock
    private ParmCardBinDefinitionSelfMapper cardBinDefinitionSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CardBinDefinitionServiceImpl cardBinDefinitionService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;

    private CardBinDefinitionReqDTO mockReqDTO;
    private CardBinDefinitionResDTO mockResDTO;
    private ParmCardBinDefinition mockEntity;
    private ParmCardBinControl mockControlEntity;
    private CardBinControlReqDTO mockControlReqDTO;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);

        // 设置静态方法返回值
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("123456");
        lenient().when(TenantUtils.getTenantId()).thenReturn("tenant001");
        lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(1001L);

        // 初始化测试数据
        setupTestData();
    }

    @AfterEach
    void tearDown() {
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
    }

    private void setupTestData() {
        // 设置CardBinDefinitionReqDTO
        mockReqDTO = new CardBinDefinitionReqDTO();
        mockReqDTO.setId("1001");
        mockReqDTO.setOrganizationNumber("123456");
        mockReqDTO.setTableId("BIN001");
        mockReqDTO.setDescription("测试BIN定义");
        mockReqDTO.setStatus("1");
        mockReqDTO.setEndNumberAlarm(100);
        mockReqDTO.setUpdateTime(LocalDateTime.now());
        mockReqDTO.setUpdateBy("test");
        mockReqDTO.setVersionNumber(1L);

        // 设置CardBinControlReqDTO
        mockControlReqDTO = new CardBinControlReqDTO();
        mockControlReqDTO.setBinSequence("001");
        mockControlReqDTO.setBinNumber("123456");
        mockControlReqDTO.setStartCardNumber("0000000001");
        mockControlReqDTO.setEndCardNumber("9999999999");

        mockReqDTO.setCardBinControlList(Arrays.asList(mockControlReqDTO));

        // 设置ParmCardBinDefinition
        mockEntity = new ParmCardBinDefinition();
        mockEntity.setId("1001");
        mockEntity.setOrganizationNumber("123456");
        mockEntity.setTableId("BIN001");
        mockEntity.setDescription("测试BIN定义");
        mockEntity.setStatus("1");
        mockEntity.setEndNumberAlarm(100);
        mockEntity.setCreateTime(LocalDateTime.now());
        mockEntity.setUpdateTime(LocalDateTime.now());
        mockEntity.setUpdateBy("test");
        mockEntity.setVersionNumber(1L);

        // 设置ParmCardBinControl
        mockControlEntity = new ParmCardBinControl();
        mockControlEntity.setId(2001L);
        mockControlEntity.setOrganizationNumber("123456");
        mockControlEntity.setTableId("BIN001");
        mockControlEntity.setBinSequence("001");
        mockControlEntity.setBinNumber("123456");
        mockControlEntity.setStartCardNumber("0000000001");
        mockControlEntity.setEndCardNumber("9999999999");
        mockControlEntity.setCreateTime(LocalDateTime.now());
        mockControlEntity.setUpdateTime(LocalDateTime.now());
        mockControlEntity.setUpdateBy("test");
        mockControlEntity.setVersionNumber(1);

        // 设置CardBinDefinitionResDTO
        mockResDTO = new CardBinDefinitionResDTO();
        mockResDTO.setId("1001");
        mockResDTO.setOrganizationNumber("123456");
        mockResDTO.setTableId("BIN001");
        mockResDTO.setDescription("测试BIN定义");
        mockResDTO.setStatus("1");
        mockResDTO.setEndNumberAlarm(100);
        mockResDTO.setUpdateTime(LocalDateTime.now());
        mockResDTO.setUpdateBy("test");
        mockResDTO.setVersionNumber(1L);
    }

    /**
     * 测试add方法 - 成功路径
     * 测试CardBinDefinitionServiceImpl.add(CardBinDefinitionReqDTO)方法
     */
    @Test
    void testAdd_Success() {
        // Arrange
        lenient().when(cardBinDefinitionSelfMapper.isExists(anyString(), anyString())).thenReturn(false);

        // Act
        ParameterCompare result = cardBinDefinitionService.add(mockReqDTO);

        // Assert
        assertNotNull(result);
        assertEquals("BIN001", result.getMainParmId());
        verify(cardBinDefinitionSelfMapper).isExists("123456", "BIN001");
    }

    /**
     * 测试add方法 - 记录已存在异常
     * 测试CardBinDefinitionServiceImpl.add(CardBinDefinitionReqDTO)方法
     */
    @Test
    void testAdd_RecordExists() {
        // Arrange
        lenient().when(cardBinDefinitionSelfMapper.isExists(anyString(), anyString())).thenReturn(true);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.add(mockReqDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试modify方法 - 成功路径
     * 测试CardBinDefinitionServiceImpl.modify(CardBinDefinitionReqDTO)方法
     */
    @Test
    void testModify_Success() {
        // Arrange
        lenient().when(cardBinDefinitionMapper.selectByPrimaryKey(anyString())).thenReturn(mockEntity);
        lenient().when(cardBinControlSelfMapper.selectAll(anyString(), anyString())).thenReturn(Arrays.asList(mockControlEntity));
        lenient().when(binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(anyString(), anyString(), anyString())).thenReturn(null);
        
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copy(any(ParmCardBinDefinition.class), any(CardBinDefinitionResDTO.class))).thenAnswer(invocation -> {
            CardBinDefinitionResDTO resDTO = new CardBinDefinitionResDTO();
            ParmCardBinDefinition entity = invocation.getArgument(0);
            resDTO.setId(entity.getId());
            resDTO.setTableId(entity.getTableId());
            resDTO.setOrganizationNumber(entity.getOrganizationNumber());
            resDTO.setDescription(entity.getDescription());
            return resDTO;
        });
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copy(any(CardBinDefinitionResDTO.class), eq(CardBinDefinitionReqDTO.class))).thenReturn(mockReqDTO);
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copyList(anyList(), eq(CardBinControlResDTO.class)))
                .thenReturn(Arrays.asList(new CardBinControlResDTO()));

        // Act
        ParameterCompare result = cardBinDefinitionService.modify(mockReqDTO);

        // Assert
        assertNotNull(result);
        assertEquals("BIN001", result.getMainParmId());
    }

    /**
     * 测试modify方法 - ID为空异常
     * 测试CardBinDefinitionServiceImpl.modify(CardBinDefinitionReqDTO)方法
     */
    @Test
    void testModify_IdNull() {
        // Arrange
        mockReqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.modify(mockReqDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试findPage方法 - 成功路径
     * 测试CardBinDefinitionServiceImpl.findPage(Integer, Integer, String, String, String)方法
     */
    @Test
    void testFindPage_Success() {
        // Arrange
        Page<ParmCardBinDefinition> mockPage = new Page<>();
        mockPage.setTotal(1);
        mockPage.setPages(1);
        
        pageHelperMockedStatic.lenient().when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(mockPage);
        lenient().when(cardBinDefinitionSelfMapper.selectPageByCondition(anyString(), anyString(), anyString()))
                .thenReturn(Arrays.asList(mockEntity));
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copyList(anyList(), eq(CardBinDefinitionResDTO.class)))
                .thenReturn(Arrays.asList(mockResDTO));

        // Act
        PageResultDTO<CardBinDefinitionResDTO> result = cardBinDefinitionService.findPage(1, 10, "123456", "BIN001", "测试");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getTotalPage());
        assertEquals(1, result.getData().size());
    }

    /**
     * 测试findPage方法 - 机构号为空使用默认值
     * 测试CardBinDefinitionServiceImpl.findPage(Integer, Integer, String, String, String)方法
     */
    @Test
    void testFindPage_OrgNumEmpty() {
        // Arrange
        Page<ParmCardBinDefinition> mockPage = new Page<>();
        mockPage.setTotal(0);
        mockPage.setPages(0);
        
        pageHelperMockedStatic.lenient().when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(mockPage);
        lenient().when(cardBinDefinitionSelfMapper.selectPageByCondition(anyString(), anyString(), anyString()))
                .thenReturn(Collections.emptyList());
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copyList(anyList(), eq(CardBinDefinitionResDTO.class)))
                .thenReturn(Collections.emptyList());

        // Act
        PageResultDTO<CardBinDefinitionResDTO> result = cardBinDefinitionService.findPage(1, 10, "", "BIN001", "测试");

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        verify(cardBinDefinitionSelfMapper).selectPageByCondition("123456", "BIN001", "测试");
    }

    /**
     * 测试find方法 - 成功路径
     * 测试CardBinDefinitionServiceImpl.find(String)方法
     */
    @Test
    void testFind_Success() {
        // Arrange
        lenient().when(cardBinDefinitionMapper.selectByPrimaryKey(anyString())).thenReturn(mockEntity);
        lenient().when(cardBinControlSelfMapper.selectAll(anyString(), anyString())).thenReturn(Arrays.asList(mockControlEntity));
        lenient().when(binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(anyString(), anyString(), anyString())).thenReturn(null);
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copy(any(ParmCardBinDefinition.class), any(CardBinDefinitionResDTO.class))).thenAnswer(invocation -> mockResDTO);
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copyList(anyList(), eq(CardBinControlResDTO.class)))
                .thenReturn(Arrays.asList(new CardBinControlResDTO()));

        // Act
        CardBinDefinitionResDTO result = cardBinDefinitionService.find("1001");

        // Assert
        assertNotNull(result);
        assertEquals("1001", result.getId());
        assertEquals("BIN001", result.getTableId());
    }

    /**
     * 测试find方法 - ID为空异常
     * 测试CardBinDefinitionServiceImpl.find(String)方法
     */
    @Test
    void testFind_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.find(null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试find方法 - 记录不存在异常
     * 测试CardBinDefinitionServiceImpl.find(String)方法
     */
    @Test
    void testFind_RecordNotFound() {
        // Arrange
        lenient().when(cardBinDefinitionMapper.selectByPrimaryKey(anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.find("1001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_CARD_BIN_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试findByOrgAndTableId方法 - 成功路径
     * 测试CardBinDefinitionServiceImpl.findByOrgAndTableId(String, String)方法
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId(anyString(), anyString())).thenReturn(mockEntity);
        lenient().when(cardBinControlSelfMapper.selectAll(anyString(), anyString())).thenReturn(Arrays.asList(mockControlEntity));
        lenient().when(binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(anyString(), anyString(), anyString())).thenReturn(null);
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copy(any(ParmCardBinDefinition.class), any(CardBinDefinitionResDTO.class))).thenAnswer(invocation -> mockResDTO);
        beanMappingMockedStatic.lenient().when(() -> BeanMapping.copyList(anyList(), eq(CardBinControlResDTO.class)))
                .thenReturn(Arrays.asList(new CardBinControlResDTO()));

        // Act
        CardBinDefinitionResDTO result = cardBinDefinitionService.findByOrgAndTableId("123456", "BIN001");

        // Assert
        assertNotNull(result);
        assertEquals("1001", result.getId());
        assertEquals("BIN001", result.getTableId());
    }

    /**
     * 测试findByOrgAndTableId方法 - 参数为空异常
     * 测试CardBinDefinitionServiceImpl.findByOrgAndTableId(String, String)方法
     */
    @Test
    void testFindByOrgAndTableId_ParametersEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.findByOrgAndTableId("", "BIN001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());

        exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.findByOrgAndTableId("123456", "");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试remove方法 - 成功路径
     * 测试CardBinDefinitionServiceImpl.remove(String)方法
     */
    @Test
    void testRemove_Success() {
        // Arrange
        lenient().when(cardBinDefinitionMapper.selectByPrimaryKey(anyString())).thenReturn(mockEntity);

        // Act
        ParameterCompare result = cardBinDefinitionService.remove("1001");

        // Assert
        assertNotNull(result);
        assertEquals("BIN001", result.getMainParmId());
    }

    /**
     * 测试remove方法 - ID为空异常
     * 测试CardBinDefinitionServiceImpl.remove(String)方法
     */
    @Test
    void testRemove_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.remove(null);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试remove方法 - 记录不存在异常
     * 测试CardBinDefinitionServiceImpl.remove(String)方法
     */
    @Test
    void testRemove_RecordNotFound() {
        // Arrange
        lenient().when(cardBinDefinitionMapper.selectByPrimaryKey(anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.remove("1001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_CARD_BIN_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试removeByOrgAndTableId方法 - 成功路径
     * 测试CardBinDefinitionServiceImpl.removeByOrgAndTableId(String, String)方法
     */
    @Test
    void testRemoveByOrgAndTableId_Success() {
        // Arrange
        lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId(anyString(), anyString())).thenReturn(mockEntity);
        lenient().when(cardBinDefinitionMapper.deleteByPrimaryKey(anyString())).thenReturn(1);
        lenient().doNothing().when(cardBinControlSelfMapper).delectByTableIdAndOrgNum(anyString(), anyString());

        // Act
        Boolean result = cardBinDefinitionService.removeByOrgAndTableId("123456", "BIN001");

        // Assert
        assertTrue(result);
        verify(cardBinControlSelfMapper).delectByTableIdAndOrgNum("BIN001", "123456");
        verify(cardBinDefinitionMapper).deleteByPrimaryKey("1001");
    }

    /**
     * 测试removeByOrgAndTableId方法 - 参数为空异常
     * 测试CardBinDefinitionServiceImpl.removeByOrgAndTableId(String, String)方法
     */
    @Test
    void testRemoveByOrgAndTableId_ParametersEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cardBinDefinitionService.removeByOrgAndTableId("", "BIN001");
        });
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }
}