package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocPersonalDefMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocPersonalMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalDefReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalDefResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtAllocPersonalResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocPersonal;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocPersonalDef;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmAcctPmtAllocPersonalService 测试类
 * 测试 ParmAcctPmtAllocPersonalServiceImpl 中的所有public方法
 */
@ExtendWith(MockitoExtension.class)
class ParmAcctPmtAllocPersonalServiceTest {

    @Mock
    private ParmAcctPmtAllocPersonalMapper parmAcctPmtAllocPersonalMapper;

    @Mock
    private ParmAcctPmtAllocPersonalDefMapper parmAcctPmtAllocPersonalDefMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private ParmAcctPmtAllocPersonalServiceImpl parmAcctPmtAllocPersonalService;

    /**
     * 测试 add 方法 - 成功路径
     */
    @Test
    void testAdd_Success() {
        // Arrange
        AcctPmtAllocPersonalReqDTO inputDTO = createMockAcctPmtAllocPersonalReqDTO();
        
        // Mock dependencies
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.isExists(anyString(), anyString())).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);

        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgUtilsMock.lenient().when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");
            tenantUtilsMock.lenient().when(() -> TenantUtils.getTenantId()).thenReturn("1");

            // Act
            ParameterCompare result = parmAcctPmtAllocPersonalService.add(inputDTO);

            // Assert
            assertNotNull(result);
            verify(parmAcctPmtAllocPersonalMapper).isExists(anyString(), anyString());
            verify(numberIdGenerator).generateId("1");
        }
    }

    /**
     * 测试 add 方法 - 数据已存在异常
     */
    @Test
    void testAdd_DataAlreadyExists() {
        // Arrange
        AcctPmtAllocPersonalReqDTO inputDTO = createMockAcctPmtAllocPersonalReqDTO();
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.isExists(anyString(), anyString())).thenReturn(1);

        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgUtilsMock.lenient().when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                parmAcctPmtAllocPersonalService.add(inputDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_ACCT_PMT_ALLOC_PERSONAL_FAULT.getCode(), exception.getErrCode());
            verify(parmAcctPmtAllocPersonalMapper).isExists(anyString(), anyString());
            verifyNoInteractions(numberIdGenerator);
        }
    }

    /**
     * 测试 add 方法 - 重复排序因子异常
     */
    @Test
    void testAdd_DuplicateSequenceItem() {
        // Arrange
        AcctPmtAllocPersonalReqDTO inputDTO = createMockAcctPmtAllocPersonalReqDTO();
        // 创建重复的排序因子
        AcctPmtAllocPersonalDefReqDTO def1 = createMockAcctPmtAllocPersonalDefReqDTO();
        def1.setSequenceItem("0");
        AcctPmtAllocPersonalDefReqDTO def2 = createMockAcctPmtAllocPersonalDefReqDTO();
        def2.setSequenceItem("0"); // 重复的排序因子
        inputDTO.setAcctPmtAllocPersonalDefReqList(Arrays.asList(def1, def2));
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.isExists(anyString(), anyString())).thenReturn(0);

        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgUtilsMock.lenient().when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                parmAcctPmtAllocPersonalService.add(inputDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_REPEAT_ACCT_PMT_ALLOC_PERSONAL_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试 modify 方法 - 成功路径
     */
    @Test
    void testModify_Success() {
        // Arrange
        AcctPmtAllocPersonalReqDTO inputDTO = createMockAcctPmtAllocPersonalReqDTO();
        inputDTO.setId("123456789");
        AcctPmtAllocPersonalResDTO existingResDTO = createMockAcctPmtAllocPersonalResDTO();
        AcctPmtAllocPersonalReqDTO existingReqDTO = createMockAcctPmtAllocPersonalReqDTO();
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(inputDTO.getId()))
                .thenReturn(createMockParmAcctPmtAllocPersonal());
        Mockito.lenient().when(parmAcctPmtAllocPersonalDefMapper.selectByOrgNumAndTableId(anyString(), anyString()))
                .thenReturn(Arrays.asList(createMockParmAcctPmtAllocPersonalDef()));

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.lenient().when(() -> BeanMapping.copy(any(ParmAcctPmtAllocPersonal.class), eq(AcctPmtAllocPersonalResDTO.class)))
                    .thenReturn(existingResDTO);
            beanMappingMock.lenient().when(() -> BeanMapping.copyList(anyList(), eq(AcctPmtAllocPersonalDefResDTO.class)))
                    .thenReturn(Arrays.asList(createMockAcctPmtAllocPersonalDefResDTO()));
            beanMappingMock.lenient().when(() -> BeanMapping.copy(any(AcctPmtAllocPersonalResDTO.class), eq(AcctPmtAllocPersonalReqDTO.class)))
                    .thenReturn(existingReqDTO);
            beanMappingMock.lenient().when(() -> BeanMapping.copyList(anyList(), eq(AcctPmtAllocPersonalDefReqDTO.class)))
                    .thenReturn(Arrays.asList(createMockAcctPmtAllocPersonalDefReqDTO()));

            // Act
            ParameterCompare result = parmAcctPmtAllocPersonalService.modify(inputDTO);

            // Assert
            assertNotNull(result);
            verify(parmAcctPmtAllocPersonalMapper).selectByPrimaryKey(inputDTO.getId());
        }
    }

    /**
     * 测试 modify 方法 - ID为null异常
     */
    @Test
    void testModify_NullId() {
        // Arrange
        AcctPmtAllocPersonalReqDTO inputDTO = createMockAcctPmtAllocPersonalReqDTO();
        inputDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            parmAcctPmtAllocPersonalService.modify(inputDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmAcctPmtAllocPersonalMapper);
    }

    /**
     * 测试 modify 方法 - 数据不存在异常
     */
    @Test
    void testModify_DataNotFound() {
        // Arrange
        AcctPmtAllocPersonalReqDTO inputDTO = createMockAcctPmtAllocPersonalReqDTO();
        inputDTO.setId("123456789");
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(inputDTO.getId()))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            parmAcctPmtAllocPersonalService.modify(inputDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_ACCT_PMT_ALLOC_PERSONAL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmAcctPmtAllocPersonalMapper).selectByPrimaryKey(inputDTO.getId());
    }

    /**
     * 测试 findByPage 方法 - 成功路径
     */
    @Test
    void testFindByPage_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        AcctPmtAllocPersonalReqDTO queryDTO = createMockAcctPmtAllocPersonalReqDTO();
        
        List<ParmAcctPmtAllocPersonal> mockEntityList = Arrays.asList(createMockParmAcctPmtAllocPersonal());
        List<AcctPmtAllocPersonalResDTO> mockResDTOList = Arrays.asList(createMockAcctPmtAllocPersonalResDTO());
        Page<ParmAcctPmtAllocPersonal> mockPage = new Page<>(pageNum, pageSize);
        mockPage.setTotal(1);
        mockPage.setPages(1);

        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByCondition(any(AcctPmtAllocPersonalReqDTO.class)))
                .thenReturn(mockEntityList);

        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<StringUtils> stringUtilsMock = mockStatic(StringUtils.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            pageHelperMock.lenient().when(() -> PageHelper.startPage(pageNum, pageSize))
                    .thenReturn(mockPage);
            beanMappingMock.lenient().when(() -> BeanMapping.copyList(mockEntityList, AcctPmtAllocPersonalResDTO.class))
                    .thenReturn(mockResDTOList);
            stringUtilsMock.lenient().when(() -> StringUtils.isEmpty(anyString())).thenReturn(false);
            orgUtilsMock.lenient().when(() -> OrgNumberUtils.getOrg()).thenReturn("001");

            // Act
            PageResultDTO<AcctPmtAllocPersonalResDTO> result = parmAcctPmtAllocPersonalService.findByPage(pageNum, pageSize, queryDTO);

            // Assert
            assertNotNull(result);
            assertEquals(pageNum, result.getPage());
            assertEquals(pageSize, result.getRows());
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getTotalPage());
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            
            verify(parmAcctPmtAllocPersonalMapper).selectByCondition(any(AcctPmtAllocPersonalReqDTO.class));
        }
    }

    /**
     * 测试 findByPage 方法 - 查询条件为null
     */
    @Test
    void testFindByPage_NullQueryDTO() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        List<ParmAcctPmtAllocPersonal> mockEntityList = Arrays.asList();
        List<AcctPmtAllocPersonalResDTO> mockResDTOList = Arrays.asList();
        Page<ParmAcctPmtAllocPersonal> mockPage = new Page<>(pageNum, pageSize);
        mockPage.setTotal(0);
        mockPage.setPages(0);

        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByCondition(any(AcctPmtAllocPersonalReqDTO.class)))
                .thenReturn(mockEntityList);

        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            pageHelperMock.lenient().when(() -> PageHelper.startPage(pageNum, pageSize))
                    .thenReturn(mockPage);
            beanMappingMock.lenient().when(() -> BeanMapping.copyList(mockEntityList, AcctPmtAllocPersonalResDTO.class))
                    .thenReturn(mockResDTOList);
            orgUtilsMock.lenient().when(() -> OrgNumberUtils.getOrg()).thenReturn("001");

            // Act
            PageResultDTO<AcctPmtAllocPersonalResDTO> result = parmAcctPmtAllocPersonalService.findByPage(pageNum, pageSize, null);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getData().size());
            verify(parmAcctPmtAllocPersonalMapper).selectByCondition(any(AcctPmtAllocPersonalReqDTO.class));
        }
    }

    /**
     * 测试 findById 方法 - 成功路径
     */
    @Test
    void testFindById_Success() {
        // Arrange
        String id = "123456789";
        ParmAcctPmtAllocPersonal mockEntity = createMockParmAcctPmtAllocPersonal();
        List<ParmAcctPmtAllocPersonalDef> mockDefList = Arrays.asList(createMockParmAcctPmtAllocPersonalDef());
        AcctPmtAllocPersonalResDTO expectedResDTO = createMockAcctPmtAllocPersonalResDTO();
        List<AcctPmtAllocPersonalDefResDTO> mockDefResDTOList = Arrays.asList(createMockAcctPmtAllocPersonalDefResDTO());
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(id))
                .thenReturn(mockEntity);
        Mockito.lenient().when(parmAcctPmtAllocPersonalDefMapper.selectByOrgNumAndTableId(anyString(), anyString()))
                .thenReturn(mockDefList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.lenient().when(() -> BeanMapping.copy(mockEntity, AcctPmtAllocPersonalResDTO.class))
                    .thenReturn(expectedResDTO);
            beanMappingMock.lenient().when(() -> BeanMapping.copyList(mockDefList, AcctPmtAllocPersonalDefResDTO.class))
                    .thenReturn(mockDefResDTOList);

            // Act
            AcctPmtAllocPersonalResDTO result = parmAcctPmtAllocPersonalService.findById(id);

            // Assert
            assertNotNull(result);
            assertEquals(expectedResDTO.getId(), result.getId());
            verify(parmAcctPmtAllocPersonalMapper).selectByPrimaryKey(id);
            verify(parmAcctPmtAllocPersonalDefMapper).selectByOrgNumAndTableId(anyString(), anyString());
        }
    }

    /**
     * 测试 findById 方法 - ID为null异常
     */
    @Test
    void testFindById_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            parmAcctPmtAllocPersonalService.findById(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmAcctPmtAllocPersonalMapper);
    }

    /**
     * 测试 findById 方法 - 数据不存在异常
     */
    @Test
    void testFindById_DataNotFound() {
        // Arrange
        String id = "123456789";
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(id))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            parmAcctPmtAllocPersonalService.findById(id);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_ACCT_PMT_ALLOC_PERSONAL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmAcctPmtAllocPersonalMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 remove 方法 - 成功路径
     */
    @Test
    void testRemove_Success() {
        // Arrange
        String id = "123456789";
        ParmAcctPmtAllocPersonal mockEntity = createMockParmAcctPmtAllocPersonal();
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(id))
                .thenReturn(mockEntity);

        // Act
        ParameterCompare result = parmAcctPmtAllocPersonalService.remove(id);

        // Assert
        assertNotNull(result);
        verify(parmAcctPmtAllocPersonalMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 remove 方法 - ID为null异常
     */
    @Test
    void testRemove_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            parmAcctPmtAllocPersonalService.remove(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verifyNoInteractions(parmAcctPmtAllocPersonalMapper);
    }

    /**
     * 测试 remove 方法 - 数据不存在异常
     */
    @Test
    void testRemove_DataNotFound() {
        // Arrange
        String id = "123456789";
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByPrimaryKey(id))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            parmAcctPmtAllocPersonalService.remove(id);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_ACCT_PMT_ALLOC_PERSONAL_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmAcctPmtAllocPersonalMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试 queryAcctPmtAllocPersonalByOrgTableId 方法 - 成功路径
     */
    @Test
    void testQueryAcctPmtAllocPersonalByOrgTableId_Success() {
        // Arrange
        String orgNumber = "001";
        String tableId = "TB001";
        ParmAcctPmtAllocPersonal mockEntity = createMockParmAcctPmtAllocPersonal();
        List<ParmAcctPmtAllocPersonalDef> mockDefList = Arrays.asList(createMockParmAcctPmtAllocPersonalDef());
        AcctPmtAllocPersonalResDTO expectedResDTO = createMockAcctPmtAllocPersonalResDTO();
        List<AcctPmtAllocPersonalDefResDTO> mockDefResDTOList = Arrays.asList(createMockAcctPmtAllocPersonalDefResDTO());
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByOrgNumAndTableId(orgNumber, tableId))
                .thenReturn(mockEntity);
        Mockito.lenient().when(parmAcctPmtAllocPersonalDefMapper.selectByOrgNumAndTableId(orgNumber, tableId))
                .thenReturn(mockDefList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<StringUtils> stringUtilsMock = mockStatic(StringUtils.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            
            beanMappingMock.lenient().when(() -> BeanMapping.copy(mockEntity, AcctPmtAllocPersonalResDTO.class))
                    .thenReturn(expectedResDTO);
            beanMappingMock.lenient().when(() -> BeanMapping.copyList(mockDefList, AcctPmtAllocPersonalDefResDTO.class))
                    .thenReturn(mockDefResDTOList);
            stringUtilsMock.lenient().when(() -> StringUtils.isNotBlank(anyString())).thenReturn(true);
            collectionUtilsMock.lenient().when(() -> CollectionUtils.isEmpty(mockDefList)).thenReturn(false);

            // Act
            AcctPmtAllocPersonalResDTO result = parmAcctPmtAllocPersonalService.queryAcctPmtAllocPersonalByOrgTableId(orgNumber, tableId);

            // Assert
            assertNotNull(result);
            assertEquals(expectedResDTO.getId(), result.getId());
            verify(parmAcctPmtAllocPersonalMapper).selectByOrgNumAndTableId(orgNumber, tableId);
            verify(parmAcctPmtAllocPersonalDefMapper).selectByOrgNumAndTableId(orgNumber, tableId);
        }
    }

    /**
     * 测试 queryAcctPmtAllocPersonalByOrgTableId 方法 - 数据不存在
     */
    @Test
    void testQueryAcctPmtAllocPersonalByOrgTableId_DataNotFound() {
        // Arrange
        String orgNumber = "001";
        String tableId = "TB001";
        
        Mockito.lenient().when(parmAcctPmtAllocPersonalMapper.selectByOrgNumAndTableId(orgNumber, tableId))
                .thenReturn(null);

        // Act
        AcctPmtAllocPersonalResDTO result = parmAcctPmtAllocPersonalService.queryAcctPmtAllocPersonalByOrgTableId(orgNumber, tableId);

        // Assert
        assertNotNull(result);
        verify(parmAcctPmtAllocPersonalMapper).selectByOrgNumAndTableId(orgNumber, tableId);
        verifyNoInteractions(parmAcctPmtAllocPersonalDefMapper);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的 AcctPmtAllocPersonalReqDTO 对象
     */
    private AcctPmtAllocPersonalReqDTO createMockAcctPmtAllocPersonalReqDTO() {
        AcctPmtAllocPersonalReqDTO dto = new AcctPmtAllocPersonalReqDTO();
        dto.setId("123456789");
        dto.setOrganizationNumber("001");
        dto.setTableId("TB001");
        dto.setStatus("1");
        dto.setDescription("Test Description");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        dto.setAcctPmtAllocPersonalDefReqList(Arrays.asList(createMockAcctPmtAllocPersonalDefReqDTO()));
        return dto;
    }

    /**
     * 创建测试用的 AcctPmtAllocPersonalResDTO 对象
     */
    private AcctPmtAllocPersonalResDTO createMockAcctPmtAllocPersonalResDTO() {
        AcctPmtAllocPersonalResDTO dto = new AcctPmtAllocPersonalResDTO();
        dto.setId("123456789");
        dto.setOrganizationNumber("001");
        dto.setTableId("TB001");
        dto.setStatus("1");
        dto.setDescription("Test Description");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        dto.setAcctPmtAllocPersonalDefResList(Arrays.asList(createMockAcctPmtAllocPersonalDefResDTO()));
        return dto;
    }

    /**
     * 创建测试用的 ParmAcctPmtAllocPersonal 对象
     */
    private ParmAcctPmtAllocPersonal createMockParmAcctPmtAllocPersonal() {
        ParmAcctPmtAllocPersonal entity = new ParmAcctPmtAllocPersonal();
        entity.setId("123456789");
        entity.setOrganizationNumber("001");
        entity.setTableId("TB001");
        entity.setStatus("1");
        entity.setDescription("Test Description");
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy("testUser");
        entity.setVersionNumber(1L);
        return entity;
    }

    /**
     * 创建测试用的 AcctPmtAllocPersonalDefReqDTO 对象
     */
    private AcctPmtAllocPersonalDefReqDTO createMockAcctPmtAllocPersonalDefReqDTO() {
        AcctPmtAllocPersonalDefReqDTO dto = new AcctPmtAllocPersonalDefReqDTO();
        dto.setId(987654321L);
        dto.setOrganizationNumber("001");
        dto.setTableId("TB001");
        dto.setSequenceItem("0");
        dto.setSequenceCondition("0");
        dto.setPriority("1");
        dto.setStatus("1");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        return dto;
    }

    /**
     * 创建测试用的 AcctPmtAllocPersonalDefResDTO 对象
     */
    private AcctPmtAllocPersonalDefResDTO createMockAcctPmtAllocPersonalDefResDTO() {
        AcctPmtAllocPersonalDefResDTO dto = new AcctPmtAllocPersonalDefResDTO();
        dto.setId(987654321L);
        dto.setOrganizationNumber("001");
        dto.setTableId("TB001");
        dto.setSequenceItem("0");
        dto.setSequenceCondition("0");
        dto.setPriority("1");
        dto.setStatus("1");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        return dto;
    }

    /**
     * 创建测试用的 ParmAcctPmtAllocPersonalDef 对象
     */
    private ParmAcctPmtAllocPersonalDef createMockParmAcctPmtAllocPersonalDef() {
        ParmAcctPmtAllocPersonalDef entity = new ParmAcctPmtAllocPersonalDef();
        entity.setId(987654321L);
        entity.setOrganizationNumber("001");
        entity.setTableId("TB001");
        entity.setSequenceItem("0");
        entity.setSequenceCondition("0");
        entity.setPriority("1");
        entity.setStatus("1");
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy("testUser");
        entity.setVersionNumber(1L);
        return entity;
    }
}